"use server";

import db from "@/lib/db";

export const createText = async (data: any) => {
  try {
    const text = await db.text.create({
      data: {
        sectionId: data.sectionId,
        content: data.content,
      },
      include: {
        section: {
          select: {
            projectId: true,
          },
        },
      },
    });

    return text;
  } catch (error) {
    console.error("Error creating text:", error);
    return;
  }
};

export const updateText = async (data: any) => {
  try {
    const text = await db.text.update({
      where: {
        id: data.id,
      },
      data: {
        content: data.content,
      },
      include: {
        section: {
          select: {
            projectId: true,
          },
        },
      },
    });

    return text;
  } catch (error) {
    console.error("Error updating text:", error);
    return;
  }
};
